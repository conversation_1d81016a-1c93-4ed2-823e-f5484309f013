## 请求

curl 'http://*********:28000/pms/task/completeDevTaskList.json?currentPage=1&pageNum=10&queryConditions%5B0%5D.queryRelation=AND&queryConditions%5B0%5D.queryField=pt.person_in_charge&queryConditions%5B0%5D.operation=LIKE&queryConditions%5B0%5D.queryValue=143e40d2eb47c18948a5cfa1161e2feb&queryConditions%5B1%5D.queryRelation=AND&queryConditions%5B1%5D.queryField=pt.created_date&queryConditions%5B1%5D.operation=GE&queryConditions%5B1%5D.queryValue=2025-08-29&queryConditions%5B1%5D.dataType=DATE&queryConditions%5B2%5D.queryRelation=AND&queryConditions%5B2%5D.queryField=pt.created_date&queryConditions%5B2%5D.operation=LE&queryConditions%5B2%5D.queryValue=2025-09-04+23%3A59%3A59&queryConditions%5B2%5D.dataType=DATE&_=1757041093462' \
  -H 'Accept: application/json, text/javascript, */*; q=0.01' \
  -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -b 'JSESSIONID_BIZPLAT_WEB=NjU3NWE1MTUtOGY3Yi00NDU1LWEyMWYtMzBlNzM1MzVjZjU4' \
  -H 'Pragma: no-cache' \
  -H 'Referer: http://*********:28000/pms/task/dev/taskList' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'X-Requested-With: XMLHttpRequest' \
  --insecure

curl 'http://*********:28000/pms/task/completeDevTaskList.json?currentPage=1&pageNum=10&queryConditions%5B0%5D.queryRelation=AND&queryConditions%5B0%5D.queryField=pt.real_end_date&queryConditions%5B0%5D.operation=GE&queryConditions%5B0%5D.queryValue=2025-09-04&queryConditions%5B0%5D.dataType=DATE&queryConditions%5B1%5D.queryRelation=AND&queryConditions%5B1%5D.queryField=pt.real_end_date&queryConditions%5B1%5D.operation=LE&queryConditions%5B1%5D.queryValue=2025-09-05+23%3A59%3A59&queryConditions%5B1%5D.dataType=DATE&_=1757063193167' \
  -H 'Accept: application/json, text/javascript, */*; q=0.01' \
  -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -b 'JSESSIONID_BIZPLAT_WEB=NjU3NWE1MTUtOGY3Yi00NDU1LWEyMWYtMzBlNzM1MzVjZjU4' \
  -H 'Pragma: no-cache' \
  -H 'Referer: http://*********:28000/pms/task/dev/taskList' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'X-Requested-With: XMLHttpRequest' \
  --insecure

## 响应

{
    "retCode": 0,
    "data": {
        "totalRecordsNum": 4,
        "currentPage": 1,
        "pageNum": 10,
        "totalPage": 1,
        "records": [
            {
                "id": "52c4906cca2ff19031519bee88b6bc80",
                "deleteFlag": 1,
                "createdDate": 1756952297000,
                "createdUser": "438a6c01bc7b1b96df4654472d6bf2a8",
                "createdUserName": "汪涛",
                "taskTitle": "[GD2025#980]保障金增加赎回数据",
                "personInCharge": "143e40d2eb47c18948a5cfa1161e2feb",
                "personInChargeName": "王义",
                "taskTime": 1.5,
                "endDate": 1757347200000,
                "realStartDate": 1756952412000,
                "priorityDic": "2",
                "projectId": "9a9ce39a8a43bab6aab307242560cc33",
                "projectName": "2023一体化升级-资管业务系统改造项目",
                "platId": "355ff9ca3710936721b7210370be81a4",
                "platName": "资管业务系统",
                "taskTypeDic": "4",
                "flowId": "735839",
                "flowStatus": "2",
                "taskStatusDic": "2",
                "deptName": "产品事业一部",
                "selectedFlag": false,
                "multiPersonInCharge": "6a8010f79eba59b378a90e684cdc4f17",
                "multiPersonInChargeName": "童紫菱",
                "optmTypeDic": "04",
                "isUat": 1,
                "isDeploy": 0,
                "deployTime": 1757412000000,
                "taskNode": "处理人完成任务",
                "frontLeader": "",
                "testLeader": "",
                "devOpsLeaderIdList": [
                    "143e40d2eb47c18948a5cfa1161e2feb"
                ]
            },
            {
                "id": "0d6053ab53e8923328255d6f3af5413d",
                "deleteFlag": 1,
                "createdDate": 1756951639000,
                "createdUser": "d335773a2a0e80a1df398f9844a776f1",
                "createdUserName": "刘雪",
                "taskTitle": "[GD2025#979]数据中台-对兴融e办推送逾期消息提醒中逾期金额重复计算。逾期金额统计不对",
                "personInCharge": "143e40d2eb47c18948a5cfa1161e2feb",
                "personInChargeName": "王义",
                "taskTime": 0.2,
                "endDate": 1757001600000,
                "realStartDate": 1756953069000,
                "realEndDate": 1756987727000,
                "priorityDic": "1",
                "projectId": "e07d0fc2ddfc71eb2d7d47e1a5dec65f",
                "projectName": "数据中台2024",
                "platId": "2428c388eb36718621eeacb5df62061c",
                "platName": "数据中台",
                "taskTypeDic": "4",
                "flowId": "735807",
                "flowStatus": "3",
                "taskStatusDic": "3",
                "deptName": "产品事业一部",
                "selectedFlag": false,
                "multiPersonInCharge": "143e40d2eb47c18948a5cfa1161e2feb",
                "multiPersonInChargeName": "王义",
                "optmTypeDic": "02",
                "isUat": 1,
                "isDeploy": 0,
                "deployTime": 1757066400000,
                "frontLeader": "",
                "testLeader": "",
                "devOpsLeaderIdList": [
                    "143e40d2eb47c18948a5cfa1161e2feb"
                ]
            },
            {
                "id": "ac8751321c770d06b32fcf536ce94d5e",
                "deleteFlag": 1,
                "createdDate": 1756778003000,
                "createdUser": "438a6c01bc7b1b96df4654472d6bf2a8",
                "createdUserName": "汪涛",
                "taskTitle": "[GD2025#968]过桥业务是否财政资金以及科创数据调整",
                "personInCharge": "143e40d2eb47c18948a5cfa1161e2feb",
                "personInChargeName": "王义",
                "taskTime": 0.2,
                "endDate": 1756742400000,
                "realStartDate": 1756778172000,
                "realEndDate": 1756956058000,
                "priorityDic": "2",
                "projectId": "9a9ce39a8a43bab6aab307242560cc33",
                "projectName": "2023一体化升级-资管业务系统改造项目",
                "platId": "355ff9ca3710936721b7210370be81a4",
                "platName": "资管业务系统",
                "taskTypeDic": "4",
                "flowId": "727573",
                "flowStatus": "3",
                "taskStatusDic": "3",
                "deptName": "产品事业一部",
                "selectedFlag": false,
                "multiPersonInCharge": "6a8010f79eba59b378a90e684cdc4f17",
                "multiPersonInChargeName": "童紫菱",
                "optmTypeDic": "04",
                "isUat": 1,
                "isDeploy": 0,
                "deployTime": 1756807200000,
                "frontLeader": "",
                "testLeader": "",
                "devOpsLeaderIdList": [
                    "143e40d2eb47c18948a5cfa1161e2feb"
                ]
            },
            {
                "id": "7846f9ee05bf3a9d50b66307ff763372",
                "deleteFlag": 1,
                "createdDate": 1756689240000,
                "createdUser": "b671667a59958efa091bd1909eb16783",
                "createdUserName": "杜港龙",
                "taskTitle": "[GD2025#963]再流水明细中新增提供给财务下载的结息表，结息表格式",
                "personInCharge": "143e40d2eb47c18948a5cfa1161e2feb",
                "personInChargeName": "王义",
                "taskTime": 1.0,
                "endDate": 1756656000000,
                "realStartDate": 1756693696000,
                "realEndDate": 1756874160000,
                "priorityDic": "3",
                "projectId": "39c8c9627cf3f31418cd8fa900d481e4",
                "projectName": "安晨医药发展业务系统2022年建设项目",
                "platId": "228b8cbd220897aa90053707a2f590b1",
                "platName": "安晨医药发展业务系统",
                "taskTypeDic": "4",
                "flowId": "725826",
                "flowStatus": "3",
                "taskStatusDic": "3",
                "deptName": "产品事业一部",
                "selectedFlag": false,
                "multiPersonInCharge": "143e40d2eb47c18948a5cfa1161e2feb",
                "multiPersonInChargeName": "王义",
                "optmTypeDic": "05",
                "isUat": 0,
                "isDeploy": 0,
                "deployTime": 1756720800000,
                "frontLeader": "",
                "testLeader": "",
                "devOpsLeaderIdList": [
                    "143e40d2eb47c18948a5cfa1161e2feb"
                ]
            }
        ],
        "taskTimeSum": 2.9
    }
}